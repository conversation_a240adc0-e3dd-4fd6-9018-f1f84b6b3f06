<!-- Reusable Tab Component -->
<!-- Usage: include('../components/tabComponent', { tabs: tabsArray, activeTab: 'tabId' }) -->

<% if (typeof tabs !== 'undefined' && tabs && tabs.length > 0) { %>
<div class="bg-white/5 border border-neutral-800/20 rounded-xl p-1 inline-flex">
  <% tabs.forEach(function(tab) { %>
    <button 
      id="tab-<%= tab.id %>" 
      class="tab-button flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 text-neutral-400 hover:text-white<% if (tab.id === activeTab) { %> bg-white/20 text-white<% } %>" 
      onclick="<% if (tab.onclick) { %><%- tab.onclick %><% } else { %>switchTab('<%- tab.id %>')<% } %>"<% if (tab.disabled) { %> disabled<% } %>
    >
      <% if (tab.icon) { %>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 mr-2">
          <path stroke-linecap="round" stroke-linejoin="round" d="<%- tab.icon %>" />
        </svg>
      <% } %>
      <%- tab.label %>
    </button>
  <% }); %>
</div>

<style>
.tab-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}
.tab-button.bg-white\/20,
.tab-button[class*="bg-white/20"] {
  background-color: rgba(255, 255, 255, 0.2) !important;
}
.tab-button.bg-white\/20:hover,
.tab-button[class*="bg-white/20"]:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
}
</style>
<% } %>