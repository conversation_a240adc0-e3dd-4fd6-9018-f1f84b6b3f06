<%- include('../../components/header', { title: 'Analytics' }) %>

<main class="h-screen m-auto">
  <div class="flex h-screen">
    <!-- Sidebar -->
    <div class="w-60 h-full">
      <%- include('../../components/template') %>
    </div>
    <!-- Content -->
    <div class="flex-1 p-6 overflow-y-auto pt-16">
      <div class="sm:flex sm:items-center px-8 pt-4">
        <%- include('../../components/pageTitle', {
          title: 'Analytics',
          description: 'Monitor and analyze your server performance and player activity'
        }) %>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
          <button id="refreshBtn" class="border border-neutral-800/20 block rounded-xl bg-white/5 hover:bg-white/10 text-white px-3 py-2 text-center text-sm font-medium shadow-lg transition duration-300 focus:outline focus:outline-2 focus:outline-offset-2">
            Refresh Data
          </button>
        </div>
      </div>

      <!-- Error Message -->
      <% if (errorMessage && errorMessage.message) { %>
      <div class="rounded-xl bg-red-800/10 px-6 py-4 mt-8 mx-8">
        <div class="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400 mr-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <div>
            <h3 class="text-sm font-medium text-red-400">Error</h3>
            <p class="text-sm text-red-400/50">
              <%= errorMessage.message %>
            </p>
          </div>
        </div>
      </div>
      <% } %>

      <!-- Tab Navigation -->
      <div class="px-8 mt-6">
        <%- include('../../components/tabComponent', { 
          tabs: [
            {
              id: 'playerstats',
              label: 'Player Stats',
              icon: 'M15 19.128a9.38 9.38 0 0 0 2.625.372 9.337 9.337 0 0 0 4.121-.952 4.125 4.125 0 0 0-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 0 1 8.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0 1 11.964-3.07M12 6.375a3.375 3.375 0 1 1-6.75 0 3.375 3.375 0 0 1 6.75 0Zm8.25 2.25a2.625 2.625 0 1 1-5.25 0 2.625 2.625 0 0 1 5.25 0Z'
            },
            {
              id: 'performance',
              label: 'Performance',
              icon: 'M3.75 3v11.25A2.25 2.25 0 0 0 6 16.5h2.25M3.75 3h-1.5m1.5 0h16.5m0 0h1.5m-1.5 0v11.25A2.25 2.25 0 0 1 18 16.5h-2.25m-7.5 0h7.5m-7.5 0-1 3m8.5-3 1 3m0 0-1-3m1 3-1-3m-9-3h9v-9a1.5 1.5 0 0 0-1.5-1.5H6a1.5 1.5 0 0 0-1.5 1.5v9Z'
            },
            {
              id: 'usage',
              label: 'Usage Analytics',
              icon: 'M10.5 6a7.5 7.5 0 1 0 7.5 7.5h-7.5V6ZM13.5 10.5H21A7.5 7.5 0 0 0 13.5 3v7.5Z'
            }
          ],
          activeTab: 'playerstats'
        }) %>
      </div>

      <!-- Tab Content -->
      <div class="px-8 mt-6">
        <!-- Player Stats Tab -->
        <div id="content-playerstats" class="tab-content">
          <!-- Stats Cards -->
          <dl class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            <!-- Total Players Card -->
            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6">
              <div class="flex items-center justify-between">
                <dt class="truncate text-sm font-medium text-neutral-300">Total Players</dt>
                <div class="rounded-lg bg-neutral-700/30 p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-neutral-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                  </svg>
                </div>
              </div>
              <dd id="totalPlayers" class="mt-3 text-3xl font-semibold tracking-tight text-white">0</dd>
              <p class="mt-1 text-sm text-neutral-400">Players across all servers</p>
            </div>

            <!-- Max Capacity Card -->
            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6">
              <div class="flex items-center justify-between">
                <dt class="truncate text-sm font-medium text-neutral-300">Max Capacity</dt>
                <div class="rounded-lg bg-neutral-700/30 p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-neutral-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                    <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                  </svg>
                </div>
              </div>
              <dd id="maxCapacity" class="mt-3 text-3xl font-semibold tracking-tight text-white">0</dd>
              <p class="mt-1 text-sm text-neutral-400">Total player capacity</p>
            </div>

            <!-- Online Servers Card -->
            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6">
              <div class="flex items-center justify-between">
                <dt class="truncate text-sm font-medium text-neutral-300">Online Servers</dt>
                <div class="rounded-lg bg-neutral-700/30 p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-neutral-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect x="2" y="2" width="20" height="8" rx="2" ry="2"></rect>
                    <rect x="2" y="14" width="20" height="8" rx="2" ry="2"></rect>
                    <line x1="6" y1="6" x2="6.01" y2="6"></line>
                    <line x1="6" y1="18" x2="6.01" y2="18"></line>
                  </svg>
                </div>
              </div>
              <dd id="onlineServers" class="mt-3 text-3xl font-semibold tracking-tight text-white">0</dd>
              <p class="mt-1 text-sm text-neutral-400">Servers currently online</p>
            </div>

            <!-- Utilization Card -->
            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6">
              <div class="flex items-center justify-between">
                <dt class="truncate text-sm font-medium text-neutral-300">Utilization</dt>
                <div class="rounded-lg bg-neutral-700/30 p-2">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-neutral-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                  </svg>
                </div>
              </div>
              <dd id="utilization" class="mt-3 text-3xl font-semibold tracking-tight text-white">0%</dd>
              <p class="mt-1 text-sm text-neutral-400">Player capacity utilization</p>
            </div>
          </dl>

          <!-- Player Graph -->
          <div class="bg-gradient-to-br from-neutral-800/20 to-neutral-900/10 border border-neutral-700/20 rounded-xl mb-8 shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold text-white">Player Count History</h2>
              <div class="text-xs text-neutral-400">Data collected every 5 minutes (last 48 hours)</div>
            </div>
            <div class="h-60">
              <canvas id="playerChart" class="w-full h-full"></canvas>
            </div>
            <div class="mt-4 flex justify-between items-center">
              <div class="text-xs text-neutral-400">
                <p>Double-click the Refresh button to manually trigger data collection</p>
              </div>
              <button onclick="showServerDetailsModal()" class="border border-neutral-800/20 rounded-xl bg-white/5 hover:bg-white/10 text-white px-4 py-2 text-sm font-medium shadow-lg transition duration-300 focus:outline focus:outline-2 focus:outline-offset-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                View Server Details
              </button>
            </div>
          </div>
        </div>

        <!-- Performance Tab -->
        <div id="content-performance" class="tab-content hidden">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Server Performance Cards -->
            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6">
              <h3 class="text-lg font-medium text-white mb-4">Server Load</h3>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm text-neutral-400">CPU Usage</span>
                  <span class="text-sm font-medium text-white">--</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-neutral-400">Memory Usage</span>
                  <span class="text-sm font-medium text-white">--</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-neutral-400">Disk Usage</span>
                  <span class="text-sm font-medium text-white">--</span>
                </div>
              </div>
            </div>

            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6">
              <h3 class="text-lg font-medium text-white mb-4">Network</h3>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm text-neutral-400">Bandwidth In</span>
                  <span class="text-sm font-medium text-white">--</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-neutral-400">Bandwidth Out</span>
                  <span class="text-sm font-medium text-white">--</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-neutral-400">Latency</span>
                  <span class="text-sm font-medium text-white">--</span>
                </div>
              </div>
            </div>

            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6">
              <h3 class="text-lg font-medium text-white mb-4">Uptime</h3>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span class="text-sm text-neutral-400">Current Uptime</span>
                  <span class="text-sm font-medium text-white">--</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-sm text-neutral-400">30-day Average</span>
                  <span class="text-sm font-medium text-white">--</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Usage Analytics Tab -->
        <div id="content-usage" class="tab-content hidden">
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6 text-center">
              <div class="text-3xl font-bold text-white" id="totalServersCount">--</div>
              <div class="text-sm text-neutral-400 mt-2">Total Servers</div>
            </div>
            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6 text-center">
              <div class="text-3xl font-bold text-white" id="activeUsersCount">--</div>
              <div class="text-sm text-neutral-400 mt-2">Active Users</div>
            </div>
            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6 text-center">
              <div class="text-3xl font-bold text-white" id="apiCallsCount">--</div>
              <div class="text-sm text-neutral-400 mt-2">API Calls</div>
            </div>
            <div class="overflow-hidden rounded-xl bg-white/5 border border-neutral-800/20 px-6 py-5 shadow-md hover:shadow-lg transition-all duration-300 sm:p-6 text-center">
              <div class="text-3xl font-bold text-white" id="storageUsedCount">--</div>
              <div class="text-sm text-neutral-400 mt-2">Storage Used</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</main>

<%- include('../../components/toast') %>

<!-- Server Details Dialog -->
<div id="serverDetailsModal" class="fixed inset-0 z-50 hidden bg-neutral-900 bg-opacity-80 backdrop-blur-sm flex items-center justify-center p-4">
  <div class="relative transform overflow-hidden rounded-xl bg-white dark:bg-neutral-800 px-6 py-6 text-left shadow-2xl transition-all w-full max-w-4xl border border-neutral-200 dark:border-neutral-700">
      <div class="border-b border-neutral-200 dark:border-neutral-700 pb-4 mb-4">
        <h3 class="text-lg font-semibold text-neutral-900 dark:text-white">Server Player Counts</h3>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-neutral-700/20">
          <thead>
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">Server</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">Players</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-neutral-400 uppercase tracking-wider">Version</th>
            </tr>
          </thead>
          <tbody id="serverTableBody" class="divide-y divide-neutral-700/20">
            <tr>
              <td colspan="4" class="px-6 py-4 text-center text-neutral-400">Loading server data...</td>
            </tr>
          </tbody>
        </table>
      </div>

      <div class="mt-6 flex justify-end">
        <button type="button" id="closeServerDetailsModalBtn" class="rounded-xl border border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 px-4 py-2 text-sm font-medium text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700">
          Close
        </button>
      </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js" 
        integrity="sha384-wcVzPe7I7UMNVhKvdDQvhqcHiGhQf0hTJHCXtOGU7VuXKjdKYqRyK7tciDx7E2Lb" 
        crossorigin="anonymous"></script>

<script>
// Initialize chart with empty data
let playerChart;

function initializeChart() {
  const ctx = document.getElementById('playerChart').getContext('2d');
  playerChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: [],
      datasets: [{
        label: 'Total Players',
        data: [],
        backgroundColor: 'rgba(163, 163, 163, 0.2)',
        borderColor: 'rgba(163, 163, 163, 1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointRadius: 2,
        pointBackgroundColor: 'rgba(163, 163, 163, 1)',
        pointBorderColor: '#fff',
        pointBorderWidth: 1,
        pointHoverRadius: 5,
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgba(163, 163, 163, 1)',
        pointHoverBorderWidth: 2
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          labels: {
            color: '#FFFFFF'
          }
        },
        tooltip: {
          callbacks: {
            title: function(tooltipItems) {
              return new Date(tooltipItems[0].label).toLocaleString();
            }
          }
        }
      },
      scales: {
        x: {
          ticks: {
            color: '#FFFFFF',
            maxRotation: 45,
            minRotation: 45,
            callback: function(value, index, values) {
              // Show fewer labels on x-axis for readability
              if (index % 12 === 0) {
                const date = new Date(this.getLabelForValue(value));
                return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
              }
              return '';
            }
          },
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          }
        },
        y: {
          position: 'right',
          beginAtZero: true,
          ticks: {
            color: '#FFFFFF',
            padding: 10,
            font: {
              weight: 'bold'
            }
          },
          grid: {
            color: 'rgba(255, 255, 255, 0.1)'
          },
          title: {
            display: true,
            text: 'Players',
            color: '#FFFFFF',
            font: {
              size: 12
            }
          }
        }
      }
    }
  });
}

function switchTab(tabName) {
    // Clear existing interval when switching tabs
    if (window.refreshInterval) {
        clearInterval(window.refreshInterval);
    }
    
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active state from all tabs
    document.querySelectorAll('.tab-button').forEach(tab => {
        tab.classList.remove('bg-white/20', 'text-white');
        tab.classList.add('text-neutral-400');
    });
    
    // Show selected tab content
    document.getElementById(`content-${tabName}`).classList.remove('hidden');
    
    // Add active state to selected tab
    const activeTab = document.getElementById(`tab-${tabName}`);
    activeTab.classList.remove('text-neutral-400');
    activeTab.classList.add('bg-white/20', 'text-white');
    
    // Load content based on tab and set up appropriate refresh interval
    if (tabName === 'playerstats') {
        fetchPlayerData();
        // Set up auto-refresh every 5 minutes for player stats only
        window.refreshInterval = setInterval(() => {
            fetchPlayerData();
        }, 300000);
    } else if (tabName === 'performance') {
        loadPerformanceData();
    } else if (tabName === 'usage') {
        loadUsageData();
    }
}

// Function to fetch player data (copied from existing player stats page)
async function fetchPlayerData() {
  try {
    const response = await fetch('/admin/playerstats/data');
    const data = await response.json();

    if (data.error) {
      return;
    }

    // Update stats cards
    document.getElementById('totalPlayers').textContent = data.totalPlayers;
    document.getElementById('maxCapacity').textContent = data.totalMaxPlayers;
    document.getElementById('onlineServers').textContent = data.onlineServers;

    const utilizationPercent = data.totalMaxPlayers > 0
      ? Math.round((data.totalPlayers / data.totalMaxPlayers) * 100)
      : 0;
    document.getElementById('utilization').textContent = `${utilizationPercent}%`;

    // Update server table
    const tableBody = document.getElementById('serverTableBody');
    tableBody.innerHTML = '';

    if (data.servers.length === 0) {
      const row = document.createElement('tr');
      row.innerHTML = `<td colspan="4" class="px-6 py-4 text-center text-neutral-400">No servers found</td>`;
      tableBody.appendChild(row);
    } else {
      data.servers.forEach(server => {
        const row = document.createElement('tr');
        row.className = 'hover:bg-white/5';

        const statusClass = server.online ? 'text-neutral-200 bg-neutral-700/30' : 'text-neutral-400 bg-neutral-800/30';
        const statusText = server.online ? 'Online' : 'Offline';

        row.innerHTML = `
          <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex items-center">
              <div class="ml-4">
                <div class="text-sm font-medium text-white">${server.serverName}</div>
                <div class="text-sm text-neutral-400">${server.serverId}</div>
              </div>
            </div>
          </td>
          <td class="px-6 py-4 whitespace-nowrap">
            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
              ${statusText}
            </span>
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
            ${server.playerCount} / ${server.maxPlayers}
          </td>
          <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-400">
            ${server.version || 'Unknown'}
          </td>
        `;

        tableBody.appendChild(row);
      });
    }

    // Update chart with historical data
    if (playerChart && data.historicalData && data.historicalData.length > 0) {
      const labels = data.historicalData.map(entry => new Date(entry.timestamp).toISOString());
      const playerCounts = data.historicalData.map(entry => entry.totalPlayers);

      // Add current data point
      labels.push(new Date().toISOString());
      playerCounts.push(data.totalPlayers);

      // Clear previous data to prevent memory leaks
      playerChart.data.labels = [];
      playerChart.data.datasets[0].data = [];

      // Update chart
      playerChart.data.labels = labels;
      playerChart.data.datasets[0].data = playerCounts;
      playerChart.update();
    } else if (playerChart) {
      // If no historical data, just add the current point
      const now = new Date().toISOString();

      // Clear previous data to prevent memory leaks
      playerChart.data.labels = [];
      playerChart.data.datasets[0].data = [];

      // Update chart
      playerChart.data.labels = [now];
      playerChart.data.datasets[0].data = [data.totalPlayers];
      playerChart.update();
    }

  } catch (error) {
    console.error('Error fetching player data:', error);
    showToast('Failed to load player statistics. Please try again.', 'error');
  }
}

function loadPerformanceData() {
    fetch('/api/admin/analytics/performance')
        .then(response => response.json())
        .then(data => {
            // Update performance metrics (placeholder for now)
        })
        .catch(error => {
            console.error('Error loading performance data:', error);
            showToast('Failed to load performance data. Please try again.', 'error');
        });
}

function loadUsageData() {
    fetch('/api/admin/analytics/usage')
        .then(response => response.json())
        .then(data => {
            // Update usage analytics
            document.getElementById('totalServersCount').textContent = data.totalServers || '--';
            document.getElementById('activeUsersCount').textContent = data.activeUsers || '--';
            document.getElementById('apiCallsCount').textContent = data.apiCalls || '--';
            document.getElementById('storageUsedCount').textContent = data.storageUsed || '--';
        })
        .catch(error => {
            console.error('Error loading usage data:', error);
            showToast('Failed to load usage analytics. Please try again.', 'error');
        });
}

// Manually trigger data collection
async function triggerDataCollection() {
  try {
    // Get CSRF token from meta tag
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    
    const response = await fetch('/api/admin/playerstats/collect', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'CSRF-Token': csrfToken
      }
    });
    const data = await response.json();

    if (data.success) {
      console.log('Player statistics collected successfully');
      showToast('Player statistics collected successfully', 'success');
      // Refresh the data after collection
      setTimeout(fetchPlayerData, 1000);
    } else {
      console.error('Error collecting player statistics:', data.error);
      showToast('Failed to collect player statistics', 'error');
    }
  } catch (error) {
    console.error('Error triggering data collection:', error);
    showToast('Failed to trigger data collection', 'error');
  }
}

// Server Details Modal Functions
function showServerDetailsModal() {
    document.getElementById('serverDetailsModal').classList.remove('hidden');
    fetchPlayerData();
}

function hideServerDetailsModal() {
    document.getElementById('serverDetailsModal').classList.add('hidden');
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeChart();
    switchTab('playerstats');

    // Manual refresh button
    document.getElementById('refreshBtn').addEventListener('click', () => {
        const activeTab = document.querySelector('.tab-button.bg-white\\/20');
        if (activeTab && activeTab.id === 'tab-playerstats') {
            fetchPlayerData();
        } else if (activeTab && activeTab.id === 'tab-performance') {
            loadPerformanceData();
        } else if (activeTab && activeTab.id === 'tab-usage') {
            loadUsageData();
        }
    });

    // Add collection button event listener (double-click)
    document.getElementById('refreshBtn').addEventListener('dblclick', (e) => {
        e.preventDefault();
        triggerDataCollection();
    });

    // Close button
    const closeBtn = document.getElementById('closeServerDetailsModalBtn');
    if (closeBtn) {
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            hideServerDetailsModal();
        });
    }
    
    // Backdrop click to close (clicking outside the modal content)
    document.getElementById('serverDetailsModal').addEventListener('click', function(e) {
        if (e.target === e.currentTarget) {
            hideServerDetailsModal();
        }
    });
    
    // Escape key to close
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') hideServerDetailsModal();
    });

    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
        if (window.refreshInterval) {
            clearInterval(window.refreshInterval);
        }
    });
});
</script>

<%- include('../../components/footer') %>