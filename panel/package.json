{"name": "airlink-panel", "version": "1.0.0", "description": "Airlink is a simple-to-use game server management panel", "repository": {"type": "git", "url": "https://github.com/AirlinkLabs/panel"}, "author": "AirlinkLabs", "license": "MIT", "scripts": {"build-ts": "tsc", "build": "npm run build-ts", "lint": "eslint src --fix", "serve": "node dist/app.js", "start": "npm run serve", "build:css": "tailwindcss -i ./public/tw.conf -o ./public/styles.css --watch", "dev": "npx prisma migrate dev --name init && npx prisma generate && nodemon", "format": "prettier --write src/", "migrate:dev": "npx prisma migrate dev --name init && npx prisma generate", "migrate:deploy": "npx prisma migrate deploy && npx prisma generate", "watch-ts": "tsc -w", "watch-css": "tailwindcss -i ./public/tw.conf -o ./public/styles.css --watch", "watch": "concurrently \"npm run watch-ts\" \"npm run watch-css\"", "seed": "ts-node src/handlers/cmd/seed.ts"}, "dependencies": {"axios": "^1.7.9", "bcrypt": "^5.1.1", "compression": "^1.7.5", "consola": "^3.4.2", "cookie-parser": "^1.4.7", "csurf": "^1.11.0", "ejs": "^3.1.10", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-ws": "^5.0.2", "helmet": "^8.0.0", "hpp": "^0.2.3", "minecraft-status": "^1.1.0", "multer": "^1.4.5-lts.1", "path": "^0.12.7", "ws": "^8.14.2"}, "devDependencies": {"@eslint/js": "^9.15.0", "@prisma/client": "^6.2.1", "@tailwindcss/forms": "^0.5.10", "@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.8", "@types/csurf": "^1.11.5", "@types/ejs": "^3.1.5", "@types/express": "^5.0.0", "@types/express-rate-limit": "^5.1.3", "@types/express-session": "^1.18.1", "@types/express-ws": "^3.0.5", "@types/fs-extra": "^11.0.4", "@types/helmet": "^0.0.48", "@types/hpp": "^0.2.6", "@types/multer": "^1.4.12", "@types/node": "^22.8.6", "@types/ws": "^8.5.14", "eslint": "^9.15.0", "fs-extra": "^11.2.0", "nodemon": "^3.1.9", "prettier": "^3.3.3", "prisma": "^6.2.1", "tailwindcss": "^3.4.15", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.7.3", "typescript-eslint": "^8.15.0"}}